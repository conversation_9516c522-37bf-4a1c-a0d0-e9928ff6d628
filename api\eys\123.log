http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=110101199001011234请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=110101199001011234
请求参数: {"name":"\u5f20\u4e09","idcard":"110101199001011234"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************
请求参数: {"name":"\u8def\u751c\u751c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AD%90%E9%BD%90&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AD%90%E9%BD%90&idcard=******************
请求参数: {"name":"\u674e\u5b50\u9f50","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AD%90%E9%BD%90&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AD%90%E9%BD%90&idcard=******************
请求参数: {"name":"\u674e\u5b50\u9f50","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E9%9D%99&idcard=150702198808082150请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E9%9D%99&idcard=150702198808082150
请求参数: {"name":"\u9648\u9759","idcard":"150702198808082150"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************
请求参数: {"name":"\u6797\u709c\u73a5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************
请求参数: {"name":"\u6797\u709c\u73a5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070920请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070920
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070920"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E6%83%A0%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E6%83%A0%E5%B9%B3&idcard=******************
请求参数: {"name":"\u5f20\u60e0\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************
请求参数: {"name":"\u6797\u709c\u73a5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************
请求参数: {"name":"\u6797\u709c\u73a5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070925请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070925
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070925"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%8D%9A%E5%AE%8F&idcard=350128201109050013请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%8D%9A%E5%AE%8F&idcard=350128201109050013
请求参数: {"name":"\u9648\u535a\u5b8f","idcard":"350128201109050013"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%94%90%E7%AB%8B%E7%BA%A2&idcard=341126199106232520请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%94%90%E7%AB%8B%E7%BA%A2&idcard=341126199106232520
请求参数: {"name":"\u5510\u7acb\u7ea2","idcard":"341126199106232520"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E6%A2%93%E5%87%AF&idcard=510681201101173018请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E6%A2%93%E5%87%AF&idcard=510681201101173018
请求参数: {"name":"\u5218\u6893\u51ef","idcard":"510681201101173018"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E6%B0%B4%E4%BB%99&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E6%B0%B4%E4%BB%99&idcard=******************
请求参数: {"name":"\u9ec4\u6c34\u4ed9","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B5%E9%95%BF%E7%94%9F&idcard=360602195301181512请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B5%E9%95%BF%E7%94%9F&idcard=360602195301181512
请求参数: {"name":"\u90b5\u957f\u751f","idcard":"360602195301181512"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E8%87%AA%E9%BE%99&idcard=360602195301181512请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E8%87%AA%E9%BE%99&idcard=360602195301181512
请求参数: {"name":"\u738b\u81ea\u9f99","idcard":"360602195301181512"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E6%AC%A3%E4%BB%81&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E6%AC%A3%E4%BB%81&idcard=******************
请求参数: {"name":"\u97e6\u6b23\u4ec1","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%B2%A9&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%B2%A9&idcard=******************
请求参数: {"name":"\u9ad8\u5ca9","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%86%E8%AF%97%E9%9F%B5&idcard=310106200505201629请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%86%E8%AF%97%E9%9F%B5&idcard=310106200505201629
请求参数: {"name":"\u9646\u8bd7\u97f5","idcard":"310106200505201629"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%A6%86%E5%87%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%A6%86%E5%87%AF&idcard=******************
请求参数: {"name":"\u5468\u6986\u51ef","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%AE%9A%E7%83%A8&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%AE%9A%E7%83%A8&idcard=******************
请求参数: {"name":"\u6768\u5b9a\u70e8","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%AD%90%E6%B7%87&idcard=320321201102111427请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%AD%90%E6%B7%87&idcard=320321201102111427
请求参数: {"name":"\u9ad8\u5b50\u6dc7","idcard":"320321201102111427"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%AD%90%E6%B7%87&idcard=320321201102111427请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%AD%90%E6%B7%87&idcard=320321201102111427
请求参数: {"name":"\u9ad8\u5b50\u6dc7","idcard":"320321201102111427"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%89%BA%E6%B8%B2&idcard=36062220120206391X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%89%BA%E6%B8%B2&idcard=36062220120206391X
请求参数: {"name":"\u5f20\u827a\u6e32","idcard":"36062220120206391X"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%94%A1%E5%90%91%E9%AB%98&idcard=620702201210237813请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%94%A1%E5%90%91%E9%AB%98&idcard=620702201210237813
请求参数: {"name":"\u8521\u5411\u9ad8","idcard":"620702201210237813"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%9F%AF%E5%AE%87&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%9F%AF%E5%AE%87&idcard=******************
请求参数: {"name":"\u5468\u67ef\u5b87","idcard":"******************"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%9F%AF%E5%AE%87&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%9F%AF%E5%AE%87&idcard=******************
请求参数: {"name":"\u5468\u67ef\u5b87","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%BD%B3%E6%80%A1&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%BD%B3%E6%80%A1&idcard=******************
请求参数: {"name":"\u674e\u4f73\u6021","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%B7%83%E8%99%8E&idcard=211322197711125317请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%B7%83%E8%99%8E&idcard=211322197711125317
请求参数: {"name":"\u5f20\u8dc3\u864e","idcard":"211322197711125317"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AE%87%E7%BA%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AE%87%E7%BA%AF&idcard=******************
请求参数: {"name":"\u5218\u5b87\u7eaf","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%A4%A7%E6%A0%B9&idcard=130427197908254717请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%A4%A7%E6%A0%B9&idcard=130427197908254717
请求参数: {"name":"\u6768\u5927\u6839","idcard":"130427197908254717"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804
请求参数: {"name":"\u65bd\u5947","idcard":"130227198306108804"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804
请求参数: {"name":"\u65bd\u5947","idcard":"130227198306108804"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804
请求参数: {"name":"\u65bd\u5947","idcard":"130227198306108804"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B2%E5%86%A0&idcard=130284196102044181请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B2%E5%86%A0&idcard=130284196102044181
请求参数: {"name":"\u66f2\u51a0","idcard":"130284196102044181"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B2%E5%86%A0&idcard=130284196102044181请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B2%E5%86%A0&idcard=130284196102044181
请求参数: {"name":"\u66f2\u51a0","idcard":"130284196102044181"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E5%8F%8B%E8%89%BA&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E5%8F%8B%E8%89%BA&idcard=******************
请求参数: {"name":"\u5f20\u53cb\u827a","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%82%96%E8%8A%B8&idcard=511322200810274882请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%82%96%E8%8A%B8&idcard=511322200810274882
请求参数: {"name":"\u8096\u82b8","idcard":"511322200810274882"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%91%A3%E5%BC%88%E5%8D%9A&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%91%A3%E5%BC%88%E5%8D%9A&idcard=******************
请求参数: {"name":"\u8463\u5f08\u535a","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E6%9F%8F%E5%AE%87&idcard=330226200510247036请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E6%9F%8F%E5%AE%87&idcard=330226200510247036
请求参数: {"name":"\u9648\u67cf\u5b87","idcard":"330226200510247036"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%BB%B6%E8%8A%B3&idcard=620122197809152318请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%BB%B6%E8%8A%B3&idcard=620122197809152318
请求参数: {"name":"\u5218\u5ef6\u82b3","idcard":"620122197809152318"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************
请求参数: {"name":"\u674e\u6dd1\u7aef","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************
请求参数: {"name":"\u674e\u6dd1\u7aef","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************
请求参数: {"name":"\u674e\u6dd1\u7aef","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718
请求参数: {"name":"\u5f20\u4e09","idcard":"320981200804052718"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%BD%98%E5%8D%8E%E4%B8%BD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%BD%98%E5%8D%8E%E4%B8%BD&idcard=******************
请求参数: {"name":"\u6f58\u534e\u4e3d","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042972请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042972
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042972"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%A5%E7%B4%A0%E8%BF%90&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%A5%E7%B4%A0%E8%BF%90&idcard=******************
请求参数: {"name":"\u4e25\u7d20\u8fd0","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718
请求参数: {"name":"\u5f20\u4e09","idcard":"320981200804052718"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718
请求参数: {"name":"\u5f20\u4e09","idcard":"320981200804052718"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E5%8D%8E%E4%B8%BD&idcard=33900519910812672X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E5%8D%8E%E4%B8%BD&idcard=33900519910812672X
请求参数: {"name":"\u5468\u534e\u4e3d","idcard":"33900519910812672X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E7%8E%89%E5%B3%B0&idcard=341224200402054317请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E7%8E%89%E5%B3%B0&idcard=341224200402054317
请求参数: {"name":"\u9ec4\u7389\u5cf0","idcard":"341224200402054317"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%99%9F%E6%B4%A5&idcard=330302200508307639请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%99%9F%E6%B4%A5&idcard=330302200508307639
请求参数: {"name":"\u5f90\u665f\u6d25","idcard":"330302200508307639"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%99%9F%E6%B4%A5&idcard=330302200508307639请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%99%9F%E6%B4%A5&idcard=330302200508307639
请求参数: {"name":"\u5f90\u665f\u6d25","idcard":"330302200508307639"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%85%89%E8%8A%B9&idcard=533022200311162228请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%85%89%E8%8A%B9&idcard=533022200311162228
请求参数: {"name":"\u738b\u5149\u82b9","idcard":"533022200311162228"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%91%A3%E9%AA%8F%E9%91%AB&idcard=342401200204038479请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%91%A3%E9%AA%8F%E9%91%AB&idcard=342401200204038479
请求参数: {"name":"\u8463\u9a8f\u946b","idcard":"342401200204038479"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E5%9B%BD%E4%B8%AD&idcard=51112919860205423X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E5%9B%BD%E4%B8%AD&idcard=51112919860205423X
请求参数: {"name":"\u4f55\u56fd\u4e2d","idcard":"51112919860205423X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************
请求参数: {"name":"\u8def\u751c\u751c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************
请求参数: {"name":"\u8def\u751c\u751c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BB%98%E5%A4%A9%E4%BF%8A&idcard=320381200906080090请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BB%98%E5%A4%A9%E4%BF%8A&idcard=320381200906080090
请求参数: {"name":"\u4ed8\u5929\u4fca","idcard":"320381200906080090"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%A2%81%E4%BA%91%E9%AA%A5&idcard=44098119990627325X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%A2%81%E4%BA%91%E9%AA%A5&idcard=44098119990627325X
请求参数: {"name":"\u6881\u4e91\u9aa5","idcard":"44098119990627325X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E8%BF%9B%E5%96%9C&idcard=350623200310236312请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E8%BF%9B%E5%96%9C&idcard=350623200310236312
请求参数: {"name":"\u6797\u8fdb\u559c","idcard":"350623200310236312"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%B6%E4%B9%A6%E5%9C%86&idcard=321282200502022427请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%B6%E4%B9%A6%E5%9C%86&idcard=321282200502022427
请求参数: {"name":"\u9676\u4e66\u5706","idcard":"321282200502022427"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%AD%E9%A1%BA%E9%94%8B&idcard=350583200305227412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%AD%E9%A1%BA%E9%94%8B&idcard=350583200305227412
请求参数: {"name":"\u90ed\u987a\u950b","idcard":"350583200305227412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%AD%E9%A1%BA%E9%94%8B&idcard=350583200305227412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%AD%E9%A1%BA%E9%94%8B&idcard=350583200305227412
请求参数: {"name":"\u90ed\u987a\u950b","idcard":"350583200305227412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249611请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249611
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249611"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249618请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249618
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249618"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249613请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249613
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249613"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249615请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249615
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249615"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249619请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249619
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249619"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249610请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249610
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249610"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249612请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249612
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249612"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249614请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249614
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249614"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249617请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249617
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249617"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249616请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AD%90%E6%B6%B5&idcard=330303202308249616
请求参数: {"name":"\u5218\u5b50\u6db5","idcard":"330303202308249616"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E4%BD%B3%E4%BD%B3&idcard=532627201212261528请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E4%BD%B3%E4%BD%B3&idcard=532627201212261528
请求参数: {"name":"\u6797\u4f73\u4f73","idcard":"532627201212261528"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%BF%8A%E8%B1%AA&idcard=331121201011135695请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%BF%8A%E8%B1%AA&idcard=331121201011135695
请求参数: {"name":"\u9648\u4fca\u8c6a","idcard":"331121201011135695"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%BF%8A%E8%B1%AA&idcard=331121201011135695请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%BF%8A%E8%B1%AA&idcard=331121201011135695
请求参数: {"name":"\u9648\u4fca\u8c6a","idcard":"331121201011135695"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%BF%8A%E8%B1%AA&idcard=331121201011135695请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%BF%8A%E8%B1%AA&idcard=331121201011135695
请求参数: {"name":"\u9648\u4fca\u8c6a","idcard":"331121201011135695"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9F%AF%E6%A5%9A%E6%9E%97&idcard=360481200806152221请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9F%AF%E6%A5%9A%E6%9E%97&idcard=360481200806152221
请求参数: {"name":"\u67ef\u695a\u6797","idcard":"360481200806152221"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9F%AF%E6%A5%9A%E6%9E%97&idcard=360481200806152221请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9F%AF%E6%A5%9A%E6%9E%97&idcard=360481200806152221
请求参数: {"name":"\u67ef\u695a\u6797","idcard":"360481200806152221"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%AE%B8%E6%A2%A6%E5%A9%B7&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%AE%B8%E6%A2%A6%E5%A9%B7&idcard=******************
请求参数: {"name":"\u8bb8\u68a6\u5a77","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%AE%B8%E6%A2%A6&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%AE%B8%E6%A2%A6&idcard=******************
请求参数: {"name":"\u8bb8\u68a6","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%AE%B8%E6%A2%A6%E5%A9%B7&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%AE%B8%E6%A2%A6%E5%A9%B7&idcard=******************
请求参数: {"name":"\u8bb8\u68a6\u5a77","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E8%8A%B9&idcard=321321198606112745请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E8%8A%B9&idcard=321321198606112745
请求参数: {"name":"\u674e\u82b9","idcard":"321321198606112745"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E7%88%BD%E7%88%BD&idcard=330302199812197328请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E7%88%BD%E7%88%BD&idcard=330302199812197328
请求参数: {"name":"\u5f90\u723d\u723d","idcard":"330302199812197328"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E7%88%BD%E7%88%BD&idcard=330302199211072829请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E7%88%BD%E7%88%BD&idcard=330302199211072829
请求参数: {"name":"\u5f90\u723d\u723d","idcard":"330302199211072829"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E9%9D%96%E5%96%9C&idcard=370321200603011226请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E9%9D%96%E5%96%9C&idcard=370321200603011226
请求参数: {"name":"\u5b59\u9756\u559c","idcard":"370321200603011226"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%A2%81%E6%B5%A9%E6%99%B4&idcard=370830199909261226请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%A2%81%E6%B5%A9%E6%99%B4&idcard=370830199909261226
请求参数: {"name":"\u8881\u6d69\u6674","idcard":"370830199909261226"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E9%B9%8F%E5%AE%87&idcard=320115201001150314请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E9%B9%8F%E5%AE%87&idcard=320115201001150314
请求参数: {"name":"\u738b\u9e4f\u5b87","idcard":"320115201001150314"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E9%B9%8F%E5%AE%87&idcard=320115201001150314请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E9%B9%8F%E5%AE%87&idcard=320115201001150314
请求参数: {"name":"\u738b\u9e4f\u5b87","idcard":"320115201001150314"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E7%9D%BF%E6%B4%8B&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E7%9D%BF%E6%B4%8B&idcard=******************
请求参数: {"name":"\u9ec4\u777f\u6d0b","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%B7%A6%E9%9B%85&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%B7%A6%E9%9B%85&idcard=******************
请求参数: {"name":"\u5de6\u96c5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%A7%9A%E4%BA%91%E5%A4%A9&idcard=320202200902025018请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%A7%9A%E4%BA%91%E5%A4%A9&idcard=320202200902025018
请求参数: {"name":"\u59da\u4e91\u5929","idcard":"320202200902025018"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%B2%99%E9%A3%9E%E5%BC%BA&idcard=320682200906060092请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%B2%99%E9%A3%9E%E5%BC%BA&idcard=320682200906060092
请求参数: {"name":"\u6c99\u98de\u5f3a","idcard":"320682200906060092"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E9%80%B8%E8%8F%B2&idcard=330621201302020843请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E9%80%B8%E8%8F%B2&idcard=330621201302020843
请求参数: {"name":"\u5b59\u9038\u83f2","idcard":"330621201302020843"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9C%B1%E6%AD%A6%E5%A8%81&idcard=330681199209031034请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9C%B1%E6%AD%A6%E5%A8%81&idcard=330681199209031034
请求参数: {"name":"\u6731\u6b66\u5a01","idcard":"330681199209031034"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%9C%E9%B9%8F&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E4%B8%9C%E9%B9%8F&idcard=******************
请求参数: {"name":"\u9648\u4e1c\u9e4f","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%98%89%E5%A9%B5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%98%89%E5%A9%B5&idcard=******************
请求参数: {"name":"\u674e\u5609\u5a75","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%A9%AC%E4%BF%8A%E7%86%99&idcard=511321201006077018请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%A9%AC%E4%BF%8A%E7%86%99&idcard=511321201006077018
请求参数: {"name":"\u9a6c\u4fca\u7199","idcard":"511321201006077018"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E6%99%93%E5%9F%B9&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E6%99%93%E5%9F%B9&idcard=******************
请求参数: {"name":"\u738b\u6653\u57f9","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%96%9B%E9%92%A2&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%96%9B%E9%92%A2&idcard=******************
请求参数: {"name":"\u859b\u94a2","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%A2%93%E4%BD%9F&idcard=130131201103083612请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%A2%93%E4%BD%9F&idcard=130131201103083612
请求参数: {"name":"\u66f9\u6893\u4f5f","idcard":"130131201103083612"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%A2%93%E4%BD%9F&idcard=130131201103083612请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%A2%93%E4%BD%9F&idcard=130131201103083612
请求参数: {"name":"\u66f9\u6893\u4f5f","idcard":"130131201103083612"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%BD%98%E6%AD%A3%E9%98%B3&idcard=510105199507192274请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%BD%98%E6%AD%A3%E9%98%B3&idcard=510105199507192274
请求参数: {"name":"\u6f58\u6b63\u9633","idcard":"510105199507192274"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AE%81%E7%81%AB%E7%88%B1&idcard=360202198501052365请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AE%81%E7%81%AB%E7%88%B1&idcard=360202198501052365
请求参数: {"name":"\u5b81\u706b\u7231","idcard":"360202198501052365"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AE%87%E6%B5%A9&idcard=21078120090716281X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AE%87%E6%B5%A9&idcard=21078120090716281X
请求参数: {"name":"\u674e\u5b87\u6d69","idcard":"21078120090716281X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AE%87%E6%B5%A9&idcard=211022199606050539请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AE%87%E6%B5%A9&idcard=211022199606050539
请求参数: {"name":"\u674e\u5b87\u6d69","idcard":"211022199606050539"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E5%A9%B7&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E5%A9%B7&idcard=******************
请求参数: {"name":"\u90d1\u5a77","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%BD%97%E5%85%B0&idcard=532730199309261528请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%BD%97%E5%85%B0&idcard=532730199309261528
请求参数: {"name":"\u7f57\u5170","idcard":"532730199309261528"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%B0%84%E6%9E%97%E4%B8%BD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%B0%84%E6%9E%97%E4%B8%BD&idcard=******************
请求参数: {"name":"\u5c04\u6797\u4e3d","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E5%85%83%E4%B8%B0&idcard=370830199302010013请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BB%96%E5%85%83%E4%B8%B0&idcard=370830199302010013
请求参数: {"name":"\u5ed6\u5143\u4e30","idcard":"370830199302010013"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E8%8D%A3%E6%B5%A9&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E8%8D%A3%E6%B5%A9&idcard=******************
请求参数: {"name":"\u8d75\u8363\u6d69","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BA%93%E6%96%B0%E5%AA%9B&idcard=371202200808032641请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BA%93%E6%96%B0%E5%AA%9B&idcard=371202200808032641
请求参数: {"name":"\u4e93\u65b0\u5a9b","idcard":"371202200808032641"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%89%BE%E5%85%89%E7%BA%A2&idcard=37132319820306144X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%89%BE%E5%85%89%E7%BA%A2&idcard=37132319820306144X
请求参数: {"name":"\u827e\u5149\u7ea2","idcard":"37132319820306144X"}
响应数据: {"message":"sso身份认证失败","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E4%B8%BD%E5%A8%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E4%B8%BD%E5%A8%9C&idcard=******************
请求参数: {"name":"\u97e6\u4e3d\u5a1c","idcard":"******************"}
响应数据: {"message":"sso身份认证失败","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E4%B8%BD%E5%A8%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E4%B8%BD%E5%A8%9C&idcard=******************
请求参数: {"name":"\u97e6\u4e3d\u5a1c","idcard":"******************"}
响应数据: {"message":"sso身份认证失败","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%A2%93%E4%BD%9F&idcard=130131201103083612请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B9%E6%A2%93%E4%BD%9F&idcard=130131201103083612
请求参数: {"name":"\u66f9\u6893\u4f5f","idcard":"130131201103083612"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E4%B8%96%E5%85%B0&idcard=500114200310176154请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E4%B8%96%E5%85%B0&idcard=500114200310176154
请求参数: {"name":"\u738b\u4e16\u5170","idcard":"500114200310176154"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E7%A5%81%E6%98%8E&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E7%A5%81%E6%98%8E&idcard=******************
请求参数: {"name":"\u674e\u7941\u660e","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412
请求参数: {"name":"\u97e9\u6653\u98ce","idcard":"220822200507170412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%80%9D%E5%BD%A4&idcard=321321201010015922请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%80%9D%E5%BD%A4&idcard=321321201010015922
请求参数: {"name":"\u5f90\u601d\u5f64","idcard":"321321201010015922"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E5%8F%8B%E5%BB%BA&idcard=140932201108238427请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E5%8F%8B%E5%BB%BA&idcard=140932201108238427
请求参数: {"name":"\u90d1\u53cb\u5efa","idcard":"140932201108238427"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%B0%8F%E5%AE%BE&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%B0%8F%E5%AE%BE&idcard=******************
请求参数: {"name":"\u738b\u5c0f\u5bbe","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E5%9B%BD%E5%BF%A0&idcard=510702196608245814请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E5%9B%BD%E5%BF%A0&idcard=510702196608245814
请求参数: {"name":"\u9ec4\u56fd\u5fe0","idcard":"510702196608245814"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B4%B9%E5%9B%BD%E5%BF%A0&idcard=510702196608245814请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B4%B9%E5%9B%BD%E5%BF%A0&idcard=510702196608245814
请求参数: {"name":"\u8d39\u56fd\u5fe0","idcard":"510702196608245814"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%8E%E6%97%8F%E5%85%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%8E%E6%97%8F%E5%85%89&idcard=******************
请求参数: {"name":"\u9ece\u65cf\u5149","idcard":"******************"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%8E%B9%E8%8E%B9&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%8E%B9%E8%8E%B9&idcard=******************
请求参数: {"name":"\u5f20\u83b9\u83b9","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BD%AD%E6%98%8E%E6%99%BA&idcard=362526197104110025请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BD%AD%E6%98%8E%E6%99%BA&idcard=362526197104110025
请求参数: {"name":"\u5f6d\u660e\u667a","idcard":"362526197104110025"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%B6%E6%98%8E%E5%AF%8C&idcard=532627200010211536请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%B6%E6%98%8E%E5%AF%8C&idcard=532627200010211536
请求参数: {"name":"\u9676\u660e\u5bcc","idcard":"532627200010211536"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%B5%B7%E6%B6%9B&idcard=522123200209152030请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%B5%B7%E6%B6%9B&idcard=522123200209152030
请求参数: {"name":"\u6768\u6d77\u6d9b","idcard":"522123200209152030"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412
请求参数: {"name":"\u97e9\u6653\u98ce","idcard":"220822200507170412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%80%9D%E5%BD%A4&idcard=321321201010015922请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%80%9D%E5%BD%A4&idcard=321321201010015922
请求参数: {"name":"\u5f90\u601d\u5f64","idcard":"321321201010015922"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%88%B4%E5%BF%97%E8%B4%B5&idcard=320981198810032978请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%88%B4%E5%BF%97%E8%B4%B5&idcard=320981198810032978
请求参数: {"name":"\u6234\u5fd7\u8d35","idcard":"320981198810032978"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%88%B4%E5%BF%97%E8%B4%B5&idcard=320981198810032978请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%88%B4%E5%BF%97%E8%B4%B5&idcard=320981198810032978
请求参数: {"name":"\u6234\u5fd7\u8d35","idcard":"320981198810032978"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412
请求参数: {"name":"\u97e9\u6653\u98ce","idcard":"220822200507170412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412
请求参数: {"name":"\u97e9\u6653\u98ce","idcard":"220822200507170412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412
请求参数: {"name":"\u97e9\u6653\u98ce","idcard":"220822200507170412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A9%E6%99%93%E9%A3%8E&idcard=220822200507170412
请求参数: {"name":"\u97e9\u6653\u98ce","idcard":"220822200507170412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%B5%B7%E6%B6%9B&idcard=522123200209152030请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%B5%B7%E6%B6%9B&idcard=522123200209152030
请求参数: {"name":"\u6768\u6d77\u6d9b","idcard":"522123200209152030"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%BE%8A%E5%86%A0%E4%B8%AD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%BE%8A%E5%86%A0%E4%B8%AD&idcard=******************
请求参数: {"name":"\u7f8a\u51a0\u4e2d","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%AD%90%E5%B8%8C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%AD%90%E5%B8%8C&idcard=******************
请求参数: {"name":"\u9648\u5b50\u5e0c","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E7%85%9C%E6%A1%90&idcard=130427201110256747请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%AD%99%E7%85%9C%E6%A1%90&idcard=130427201110256747
请求参数: {"name":"\u5b59\u715c\u6850","idcard":"130427201110256747"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B0%A2%E6%B0%B8%E5%81%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B0%A2%E6%B0%B8%E5%81%A5&idcard=******************
请求参数: {"name":"\u8c22\u6c38\u5065","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B0%A2%E6%B0%B8%E5%81%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B0%A2%E6%B0%B8%E5%81%A5&idcard=******************
请求参数: {"name":"\u8c22\u6c38\u5065","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X
请求参数: {"name":"\u8d75\u5b87\u4eae","idcard":"65402120070427373X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%99%AF%E5%AA%9B&idcard=622923199908280826请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E6%99%AF%E5%AA%9B&idcard=622923199908280826
请求参数: {"name":"\u6768\u666f\u5a9b","idcard":"622923199908280826"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X
请求参数: {"name":"\u8d75\u5b87\u4eae","idcard":"65402120070427373X"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X
请求参数: {"name":"\u8d75\u5b87\u4eae","idcard":"65402120070427373X"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X
请求参数: {"name":"\u8d75\u5b87\u4eae","idcard":"65402120070427373X"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B5%B5%E5%AE%87%E4%BA%AE&idcard=65402120070427373X
请求参数: {"name":"\u8d75\u5b87\u4eae","idcard":"65402120070427373X"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E9%9B%A8%E9%98%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%81%E9%9B%A8%E9%98%B3&idcard=******************
请求参数: {"name":"\u4e01\u96e8\u9633","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E6%98%A5%E5%8D%8E&idcard=320586198702152438请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E6%98%A5%E5%8D%8E&idcard=320586198702152438
请求参数: {"name":"\u4f55\u6625\u534e","idcard":"320586198702152438"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E6%98%A5%E5%8D%8E&idcard=320582198709120812请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E6%98%A5%E5%8D%8E&idcard=320582198709120812
请求参数: {"name":"\u4f55\u6625\u534e","idcard":"320582198709120812"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E6%98%A5%E5%8D%8E&idcard=320586199111155412请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E6%98%A5%E5%8D%8E&idcard=320586199111155412
请求参数: {"name":"\u4f55\u6625\u534e","idcard":"320586199111155412"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%8F%B2%E4%B9%A6%E7%A6%8F&idcard=342501198409227818请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%8F%B2%E4%B9%A6%E7%A6%8F&idcard=342501198409227818
请求参数: {"name":"\u53f2\u4e66\u798f","idcard":"342501198409227818"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BA%8E%E5%8D%B0%E5%9D%A6&idcard=130534194503266619请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BA%8E%E5%8D%B0%E5%9D%A6&idcard=130534194503266619
请求参数: {"name":"\u4e8e\u5370\u5766","idcard":"130534194503266619"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%A2%93%E6%A1%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%A2%93%E6%A1%89&idcard=******************
请求参数: {"name":"\u5f90\u6893\u6849","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E6%9C%8B&idcard=320722200207062311请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%83%91%E6%9C%8B&idcard=320722200207062311
请求参数: {"name":"\u90d1\u670b","idcard":"320722200207062311"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%A0%BC%E6%A0%BC&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%A0%BC%E6%A0%BC&idcard=******************
请求参数: {"name":"\u674e\u683c\u683c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

