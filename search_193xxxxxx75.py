import os
import re

log_dir = 'api_logs'
# 搜索开头150结尾79的11位手机号的正则表达式
phone_pattern = r'150\d{6}79'

lines_found = []

for root, dirs, files in os.walk(log_dir):
    for file in files:
        file_path = os.path.join(root, file)
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    # 使用正则表达式搜索匹配的手机号
                    matches = re.findall(phone_pattern, line)
                    if matches:
                        # 如果找到匹配的手机号，记录该行和找到的手机号
                        phone_numbers = ', '.join(matches)
                        lines_found.append(f"{file_path}:{line_num} - 找到手机号: {phone_numbers} - {line.strip()}")
        except Exception as e:
            pass  # 忽略无法读取的文件

with open('sjh.txt', 'w', encoding='utf-8') as out:
    for line in lines_found:
        out.write(line + '\n')
